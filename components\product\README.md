# Pagination Component

A comprehensive pagination component built with shadcn/ui components and Next.js App Router.

## Features

- ✅ **URL-based pagination** - Page state is stored in URL parameters (`?page=1`)
- ✅ **First/Last page navigation** - Quick navigation to first and last pages
- ✅ **Previous/Next navigation** - Step-by-step navigation
- ✅ **Smart page number display** - Shows ellipsis for large page ranges
- ✅ **Default page handling** - Defaults to page 1 if no URL parameter
- ✅ **Responsive design** - Works on mobile and desktop
- ✅ **TypeScript support** - Fully typed with TypeScript
- ✅ **Accessibility** - Proper ARIA labels and keyboard navigation

## Usage

### Basic Usage

```tsx
import { PaginationComponent } from "@/components/product/ProductPagination";

function MyComponent() {
  const totalItems = 250;
  const pageSize = 10;

  return (
    <PaginationComponent
      pageSize={pageSize}
      totalItems={totalItems}
      className="justify-center" // optional
    />
  );
}
```

### Advanced Usage with Hook

```tsx
import { PaginationComponent, usePagination } from "@/components/product/ProductPagination";

function MyComponent() {
  const totalItems = 250;
  const pageSize = 10;

  const {
    currentPage,
    totalPages,
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToPreviousPage,
    goToNextPage,
    createPageURL,
  } = usePagination(pageSize, totalItems);

  return (
    <div>
      <p>Page {currentPage} of {totalPages}</p>
      
      {/* Use the component */}
      <PaginationComponent
        pageSize={pageSize}
        totalItems={totalItems}
      />
      
      {/* Or use programmatic navigation */}
      <button onClick={goToFirstPage}>First</button>
      <button onClick={goToLastPage}>Last</button>
    </div>
  );
}
```

## Props

### PaginationComponent

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `pageSize` | `number` | ✅ | Number of items per page |
| `totalItems` | `number` | ✅ | Total number of items |
| `className` | `string` | ❌ | Additional CSS classes |

### usePagination Hook

Returns an object with the following properties:

| Property | Type | Description |
|----------|------|-------------|
| `currentPage` | `number` | Current page number (from URL) |
| `totalPages` | `number` | Total number of pages |
| `goToPage` | `(page: number) => void` | Navigate to specific page |
| `goToFirstPage` | `() => void` | Navigate to first page |
| `goToLastPage` | `() => void` | Navigate to last page |
| `goToPreviousPage` | `() => void` | Navigate to previous page |
| `goToNextPage` | `() => void` | Navigate to next page |
| `createPageURL` | `(page: number) => string` | Create URL for specific page |

## URL Parameter

The component automatically manages the `page` URL parameter:

- `?page=1` - First page
- `?page=2` - Second page
- No parameter - Defaults to page 1

The page parameter is preserved with other URL parameters.

## Examples

See `ProductPaginationDemo.tsx` for a complete working example.

## Dependencies

- Next.js App Router
- shadcn/ui Pagination components
- React hooks (useState, useCallback, useMemo)
